-- DROP FUNCTION public.tms_create_va_rating(json);

CREATE OR REPLACE FUNCTION public.tms_create_va_rating(form_data json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
DECLARE
    status         boolean := false;
    message        text := 'Internal_error';
    resp_data      json;

    -- audit info
    usr_id_        uuid;
    org_id_        int4;
    ip_address_    text;
    user_agent_    text;

    -- payload fields
    session_id_           text;
    additional_comments_  text;
    sugg_feedback_        text;
    usr_prompt_           text;
    va_response_          json;
    good_rating_          boolean;
    bad_rating_           boolean;

    entry_id_ bigint;
    is_entry_exists boolean := false;
BEGIN
    -- extract values from form_data JSON
    usr_id_              := form_data ->> 'usr_id';
    org_id_              := (form_data ->> 'org_id')::int;
    ip_address_          := form_data ->> 'ip_address';
    user_agent_          := form_data ->> 'user_agent';

    session_id_          := form_data ->> 'session_id';
    additional_comments_ := form_data ->> 'additional_comments';
    sugg_feedback_       := form_data ->> 'sugg_feedback';
    usr_prompt_          := form_data ->> 'usr_prompt';
    va_response_         := form_data ->  'va_response';
    good_rating_         := (form_data ->> 'good_rating')::boolean;
    bad_rating_          := (form_data ->> 'bad_rating')::boolean;

    -- -- optional deduplication
    -- SELECT EXISTS (
    --     SELECT 1
    --     FROM public.cl_tx_va_rating
    --     WHERE session_id = session_id_
    --       AND org_id = org_id_
    --       AND good_rating IS NOT DISTINCT FROM good_rating_
    --       AND bad_rating IS NOT DISTINCT FROM bad_rating_
    --     LIMIT 1
    -- ) INTO is_entry_exists;

    -- IF is_entry_exists THEN
    --     status := true;
    --     message := 'entry_already_exists';
    --     resp_data := json_build_object('is_entry_exists', true);
    --     RETURN json_build_object('status', status, 'code', message, 'data', resp_data);
    -- END IF;

    -- insert new record
    INSERT INTO public.cl_tx_va_rating (
        session_id,
        additional_comments,
        sugg_feedback,
        usr_prompt,
        va_response,
        good_rating,
        bad_rating,
        org_id,
        c_by,
        txn_time,
        c_meta
    ) VALUES (
        session_id_,
        additional_comments_,
        sugg_feedback_,
        usr_prompt_,
        va_response_,
        good_rating_,
        bad_rating_,
        org_id_,
        usr_id_,
        now() AT TIME ZONE 'utc',
        ROW(ip_address_, user_agent_, now() AT TIME ZONE 'utc')
    )
    RETURNING db_id INTO entry_id_;

    status := true;
    message := 'success';
    resp_data := json_build_object('entry_id', entry_id_);

    RETURN json_build_object('status', status, 'code', message, 'data', resp_data);
END;
$function$
;
